Fluent UI is a collection of robust components designed to make it simple for you to create consistent web experiences using the Fluent Design Language.

## Using Fluent UI

For information about basics of using Fluent UI, and details of the different projects located in this repo, see the [README](https://github.com/microsoft/fluentui/blob/master/README.md).

## Contributing

[Setting up your development environment](https://github.com/microsoft/fluentui/wiki/Setup#basic-setup)

[Contributing to `@fluentui/react` and related packages](Contributing/Contributing)

[Contributing to `@fluentui/react-northstar` and related packages](https://github.com/microsoft/fluentui/blob/master/packages/fluentui/CONTRIBUTING.md)

## `fluentui/react` Version 9 is released 🎉

See the [documentation website](https://react.fluentui.dev) for more information.

## `@fluentui/react` Version 8

See the [release notes](Version-8-release-notes) for more information.
