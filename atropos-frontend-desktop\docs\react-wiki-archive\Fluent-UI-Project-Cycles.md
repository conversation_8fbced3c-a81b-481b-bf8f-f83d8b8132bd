We use Monthly Project Cycles to execute on work. These help our team focus on specific work each month, stay on top of who is working on what, and generally make progress towards shared goals.

The following outlines current and past Project Cycles for the Fluent UI projects

## Fluent UI React

- [Create a new Proposal](https://github.com/microsoft/fluentui/issues/new?body=%23%20Project%20Proposal%0A%0A%23%23%20Problem%0A%23%23%20Appetite%0A%23%23%20Solution%0A%23%23%20Risks%20%28Rabbit%20holes%29%0A%23%23%20Out%20of%20scope%20%28No-gos%29&labels=Type:%20Feature)

Milestones tracking each project cycle

- https://github.com/microsoft/fluentui/milestones
