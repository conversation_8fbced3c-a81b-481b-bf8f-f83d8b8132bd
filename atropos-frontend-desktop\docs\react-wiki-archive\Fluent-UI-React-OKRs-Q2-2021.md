### Enable AppBar and MeControl

- Deliver production ready Icon, Divider, Flex, Tooltip, Button, Badge
- Deliver Avatar, Menu, MenuButton, Popup, Text, Focus[Trap]Zone
- Deliver Popup, Accordion, Header
- Deliver Input

### Enable Partners to be successfully implemented with vNext components

- Deliver foundational features [Stabilize react-components](https://github.com/microsoft/fluentui/projects/42)
  - All Pri 1 issues addressed by end of quarter

### Improve developer productivity and satisfaction for vNext components

- Reduce build failure rate (goal TBD) e.g. Fluent UI React - CI 14 day pass rate 81% -> 95%
- Reduce build time (goal TBD) can we get 5 or 10 min CI build for vNext?
- CI errors reproducible locally (goal TBD) as close to 100% as we can get

### Accessibility: @fluentui/react improvements by End of April 

- Meet High Impact MAS bugs targets
- Meet Medium Impact MAS bugs targets
- Maintain targets

### Set clear expectations and timelines with our core 1st party partners

- Deliver a plan for testing value with partners: Performance, Bundle Size, Accessibility, API Surface
- Establish Fluent UI vNext component roll out plan jointly with partners
