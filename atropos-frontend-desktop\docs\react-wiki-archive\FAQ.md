# Fluent UI React FAQ

## _Q. What is Fluent UI React?_

Fluent UI React (formerly Fabric React) is a responsive, mobile-first, open-source collection of robust components designed to make it quick and simple for you to create web experiences using the Fluent design language.

## _Q. Who uses Fluent UI React?_

React components from Fluent UI React are used by many teams across O365 and Microsoft for building their experiences. Some examples are SharePoint, OneDrive, Outlook, VisualStudio team services, ...

These components are also used by third-party developers, in particular those building components using the SharePoint Framework.

## _Q. How do I get started with Fluent UI React?_

Start from our [wiki homepage](Home) or the ["Get started" section](https://developer.microsoft.com/en-us/fluentui#/get-started/web) on our website.

## _Q. Where is the official website located?_

https://developer.microsoft.com/fluentui

## _Q. I am seeing a bug. Where can I open an issue?_

Please open all issues at our [GitHub issues](https://github.com/microsoft/fluentui/issues) location.

Things to remember while opening an issue:

- Please fill out as many details as you can.
- We **highly encourage** you to submit PRs for issues.

## _Q. Can I contribute to Fluent UI React?_

Fluent UI React is an open-source distributed project. Lots of developers from inside and outside Microsoft contribute to this project. We highly encourage contributions. There is a core team that works hard to stay on top of the issues and pull requests.

## _Q. Why should I contribute?_

Fluent UI React is an open-source distributed project. If you are building a new app inside O365 or Microsoft, this project will potentially save you lots of time, resources and headaches. We encourage all developers who use Fluent UI React to return a small amount to the community. If you notice bugs, we encourage you to not only open an issue on it but also help fix it. If you have a component that you believe others should use, feel motivated and encouraged to contribute that component back to the community. Sharing is caring. Many developers contribute outside their primary job responsibilities. Additionally, there is plenty to learn. We use a lot of cutting edge best practices used in the industry. Learning those can help you in your career.

## _Q. How do I communicate with a Fluent UI React core team member?_

We have a very active community both outside and inside Microsoft. Please use the Microsoft [Teams channel](<https://teams.microsoft.com/l/channel/19%3a86b094239256467da9dfa96ba0897ca2%40thread.skype/Fluent%2520UI%2520React%2520(Web)?groupId=ffe264f2-14d0-48b5-9384-64f808b81294&tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47>) for communication from within Microsoft. And a [GitHub issue](https://github.com/microsoft/fluentui/issues) from outside Microsoft. We triage these issues at least once a week.

## _Q. How do I contribute to Fluent UI React?_

Start by reading our [Contributing](Contributing) document. Please point out any missing or incorrect details.

## _Q. Can I become a part of the Fluent UI core team?_

Yes, you totally can. You will need to help out with reviewing pull requests and triaging and fixing issues. For now, only Microsoft employees can be a part of the core team. We are looking into changing that policy to allow non-Microsoft folks to be a part of the core team.

## _Q. My pull request has been hanging for a long time. What do I do?_

We are very proactive and work hard to keep the outstanding pull request count low. But if your pull request is non-trivial and stuck for a long time, please feel free to tag the owners or contact them internally for help.

## _Q. Where do I read about specific best practices?_

Please refer to the following documents.

- [**Contributing fixes and new components**](Contributing)
- [**Accessibility**](Accessibility)
- [**Component Design**](Component-Design)
- [**Styling**](Component-Styling)
- [**Theming**](Theming)
- [**Testing**](Testing)
- [**Advanced**](Advanced-Usage)
- [**Browse Support**](Browser-Support)

## _Q. When and how does the issue Triage work?_

The Fluent UI React core team works hard to stay on top of the open issues. We have a team member assigned to "shield" each week to triage new issues as they come in.

### Following issues will qualify as high priority

We try to fix high priority bugs ASAP.

- Is this issue blocking the consumer?
- Is this a regression? If yes, how and when did it get in?\* Is this a very serious bug?

Other bugs will qualify as Normal or lower priority and will get fixed as a part of the normal release cadence.

From time to time we do plan to close issues that are very old and have no activity.

## _Q. Will there a Fluent UI for Angular?_

A Fluent UI project for Angular is not currently on our roadmap. However, we do have a new [web components package](https://aka.ms/fluentui-web-components) (`@fluentui/web-components`) in progress which can interoperate with Angular.

You can also use the [UI Fabric Core](https://github.com/OfficeDev/office-ui-fabric-core) CSS classes and SCSS variables/mixins for access to our palette, typography and iconography within any framework.
