This roadmap represents the currently planned work for Fluent UI React

As an open source project, features not tracked on this roadmap may be submitted by the community. To make a request for a feature to be added to this roadmap, please submit an [issue on github](https://github.com/microsoft/fluentui/issues/new/choose).

| ⚠️ Subject to Change
|:-----------------------------------------|
|Our roadmap provides insight into our current planning and is subject to change at any time based on new priorities, feature requests, or learnings. While we will do our best to maintain a stable roadmap, this document does not represent a firm commitment.|

| ❓ Got questions |
| :--------------- |

|Post any questions or feedback on our Roadmap over on the [Q&A Discussions](https://github.com/microsoft/fluentui/discussions/categories/q-a)

## Q4 (Oct-Dec) 2021

### Deliver a ~public~ preview of @fluentui/react-components

_[Update] we're not announcing @fluentui/react-components publicly until later. Our focus right now is on internal partners and getting their feedback._

This quarter our goal is to finish all known breaking changes for the core component architecture and release an RC. We also will deliver more components for close internal partners to pick up and use in their applications. We will continue to gather internal partner feedback and validate the architecture and components in large scale web applications.

- Ship a RC component architecture and theme shape
  - Progress: [@fluentui/react-components v9.0.0 Project](https://github.com/orgs/microsoft/projects/182/views/4)
  - Deliverable: @fluentui/react-components@RC
- Ship Phase 1 and 2 components at Stable Quality (in @fluentui/react-components)
  - Avatar Button (and variants), Icon, Link, Divider, FluentProvider, Menu, Popover, Text, Tooltip, Accordion, Badge
  - Progress: [Fluent UI React vNext Component Roadmap](vNext-Component-Roadmap)
- Deliver Phase 3 and 4 components converged and released as Alpha & Beta Quality:
  - Alpha: Input, Dropdown
  - Beta: Image, Checkbox, Label, Radio, Slider, Switch
  - Progress: [Fluent UI React vNext Component Roadmap](vNext-Component-Roadmap)

### Deliver beta quality developer documentation 

- Author and publish a set of guides
  - Theme compatibility guide
  - Migration guide for library concepts
  - Getting started guide
- 100% coverage of @fluentui/react-components with docs /examples

### Initial partners are excited and enthusiastic to integrate vNext components

- Working primarily with internal partners at this point for tight feedback loops

## Q1 (Jan-Mar) 2022

### Deliver an initial release of @fluentui/react-components (with missing components)

This quarter our goal is to have a stable component architecture that close partners can depend on without fear of breaking changes. We are also targeting to have Phase 1 through 5 stable as well, which will enable close internal partners to incrementally upgrade to vNext components. This is important to bring benefit to our partners now, as well as getting real production use feedback on these components early.

- Ship a stable (v9.0) component architecture and theme shape
  - Progress: [@fluentui/react-components v9.0.0 Project](https://github.com/orgs/microsoft/projects/182/views/4)
  - Deliverable: @fluentui/react-components@9.0
- Phase 1 - 5 components at Stable Quality (in @fluentui/react-components)
  - Progress: [Fluent UI React vNext Component Roadmap](vNext-Component-Roadmap) (Phasing coming soon to component roadmap)

## Q2 (Apr-Jun) 2022

### Deliver a complete first version of @fluentui/react-components

This quarter we are targeting having a broadly React component library that folks building new applications could pick up and use.
We won't have all 70+ components that exist in @fluentui/react and @fluentui/react-northstar, but the library should be valuable for many new applications.

- Ship a stable (v9.0) component architecture and theme shape
  - Progress: [@fluentui/react-components v9.0.0 Project](https://github.com/orgs/microsoft/projects/182/views/4)
  - Deliverable: @fluentui/react-components@9.0
- Phase 1 - 8 components at Stable Quality (in @fluentui/react-components)
  - Progress: [Fluent UI React vNext Component Roadmap](vNext-Component-Roadmap) (Phasing coming soon to component roadmap)
