For instructions on shaping a project see here: [Shaping a Project](Fluent-UI-Dev-Process#what-is-a-project-proposal)

# Project template

_Below are the key ingredients for a project (pitch in shape up terms). Feel free to use them as a template, or go free form. Like when baking a delcious cake :cake: make sure to include all 5 ingredients. If using this as a template, simply delete all the text in italics, and provide your own._

### Problem

_From: [Problem guidance](https://basecamp.com/shapeup/1.5-chapter-06#ingredient-1-problem)_
_The best problem definition consists of a single specific story that shows why the status quo doesn’t work._

### Appetite

_From: [Appetite guidance](https://basecamp.com/shapeup/1.5-chapter-06#ingredient-2-appetite)_
_Think of this as another part of the problem definition. We want to solve this problem, but we also need to do it in a way that will leave time to solve other problems. Here we depart from Shape Up, and allow for timeframes or appetites of 1-3 weeks._

### Solution

_The solution is what makes this Project "Shaped". Without a specific solution defined we leave too much ambiguity and risk for the Project team to figure out while coding. This is not, however, a detailed spec or list of task. It is good to leave details open for the team to decide on while building the solution_

### Risks (Rabbit holes)

_From: [Rabbit hole guidance](https://basecamp.com/shapeup/1.5-chapter-06#ingredient-4-rabbit-holes)_
_Another key aspect of shaping is de-risking. This involves identifying potential issues and complications in the solution. These may be non-obvious cases where the solution doesn't work. These could be constraints from other parts of the system (dependencies or dependent code). This aspect of shaping is what typically requires the most experience and understanding of the domain. This is likely somethign we will all collectively get better at with practice._

### Out of scope (No-gos)

_From: [No-gos guidance](https://basecamp.com/shapeup/1.5-chapter-06#ingredient-5-no-gos)_
_A key way to deal with complicated risks or issues with the solution, is to decide a particular funcionatliy is out of scope. If reducing the scope to remove a risk or issue does not prevent us from fulfilling the original problem, then it is fine. Reducing scope may require reaching out to the original customer that had the problem we are solving, or working with a representive of the customer on our team._
