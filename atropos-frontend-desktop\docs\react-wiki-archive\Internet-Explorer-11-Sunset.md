Starting on June 15th, 2022, Fluent UI v8 will no longer support Internet Explorer 11 or earlier, aligning Fluent UI v8's [Internet Explorer support with Windows 10](https://blogs.windows.com/windowsexperience/2021/05/19/the-future-of-internet-explorer-on-windows-10-is-in-microsoft-edge/) and [Fluent UI v9](https://github.com/microsoft/fluentui/blob/master/rfcs/react-components/convergence/ending-ie11-support.md).

For Fluent UI v8 this means that any changes made to the library from June 15th, 2022 onward may break compatibility with Internet Explorer 11. With the [upcoming release of Fluent UI v9](https://github.com/microsoft/fluentui/wiki/Fluent-UI-React-v9-Release) most of our development effort is focused on this new version of Fluent.

**There are no plans to remove existing IE11 compatibility from Fluent UI v8**. Any breaks to Fluent UI v8's IE11 compatibility will likely be the result of bug fixes.
