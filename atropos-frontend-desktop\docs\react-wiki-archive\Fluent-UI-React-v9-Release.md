## Fluent UI React v9 is released! 🎉

We have released our first stable release of Fluent UI React v9 - [find it on npm](https://www.npmjs.com/package/@fluentui/react-components). This a stable foundation for our new component model bringing drastic improvements in Performance, Accessibility, Customization, and Design to Code. We will continue to improve upon this release and add more components over time, see our Component Roadmap linked below for upcoming components.

## Component Roadmap

We've updated the spot of our [Component Roadmap](Fluent-UI-React-v9-Component-Roadmap). Here you'll get a sense of our upcoming component build out schedule. What's stable in the coming relase as well as what's to follow fast, and proposed in the future.

## Documentation

You can find the latest documentation of Fluent UI React v9 at https://react.fluentui.dev

## Migration from `v0/v8` to `v9`

Migration is unique to each project and there is a dedicated section focusing on migration in our documentation. It's recommended to start there to understand what's changing, how to use the two libraries side-by-side, and strategies on how to approach migration for your project.

1. [Upgrading from v8 to v9](https://react.fluentui.dev/?path=/docs/concepts-migration-from-v8-components-checkbox-migration--docs)
2. [Upgrading from v0 to v9](https://react.fluentui.dev/?path=/docs/concepts-migration-from-v0-components-checkbox-migration--docs)
3. Example app of [upgrading from v8 to v9](https://github.com/GeoffCox/fluentui-app-v8-v9)

## Questions

If you have questions regarding the Fluent UI React v9 release, feel free to contact us on the [Fluent UI React v9 Discussion](https://github.com/microsoft/fluentui/discussions/23183)

## Release Planning

The Fluent UI React team is getting ready to release our first stable release, we've gathering up our [final list breaking changes](https://github.com/microsoft/fluentui/issues/22130#issuecomment-1069566852) and we'll have a few more RC releases to help teams plan and migrate to the final stable release. This wiki will help provide some pointers around the recent RC builds, links to breaking changes, and a release target for the stable release.

## Key Dates and Releases

1. ✅ 5/23 - Pre-breaking change RC Release - `9.0.0-rc.11` - [link to npm](https://www.npmjs.com/package/@fluentui/react-components/v/9.0.0-rc.11)
1. ✅ 5/23 - Post-breaking change RC Release - `9.0.0-rc.12` - [link to npm](https://www.npmjs.com/package/@fluentui/react-components/v/9.0.0-rc.12)
   - [Includes the list of breaking changes](https://github.com/microsoft/fluentui/issues/22130#issuecomment-1069566852)
1. ✅ 5/26 - RC Release - `9.0.0-rc.13` - [link to npm](https://www.npmjs.com/package/@fluentui/react-components/v/9.0.0-rc.13)
   - rc.13 will include bug fixes from internal bug tests
1. ✅ 5/31 - Final\* RC Release - `9.0.0-rc.14` - [link to npm](https://www.npmjs.com/package/@fluentui/react-components/v/9.0.0-rc.14)
   - _\*If blocking bugs are found additional RC versions may be released_
1. ✅ June 2022 - Community and Partner testing and feedback
1. ✅ End of June 2022 - Final Stable Release - `9.0.0` - [link to npm](https://www.npmjs.com/package/@fluentui/react-components)
