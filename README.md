# 🍽️ Atropos POS System

Modern, kapsamlı restoran yönetim sistemi. NestJS backend ve Electron desktop uygulaması ile geliştirilmiş enterprise-grade POS çözümü.

## 📋 İçindekiler

- [<PERSON><PERSON><PERSON><PERSON>](#-<PERSON><PERSON><PERSON><PERSON>)
- [<PERSON><PERSON><PERSON><PERSON><PERSON>ack](#-teknoloji-stack)
- [<PERSON><PERSON><PERSON>](#-kurulum)
- [<PERSON><PERSON><PERSON><PERSON>](#-kullanım)
- [API Dokümantasyonu](#-api-dokümantasyonu)
- [Veritabanı Şeması](#-veritabanı-şeması)
- [Katkıda Bulunma](#-katkıda-bulunma)
- [Lisa<PERSON>](#-lisans)

## ✨ Özellikler

### 🏪 Temel POS Özellikleri
- **Sipariş Yönetimi**: Masa, paket servis, gel-al siparişleri
- **Ürün Kataloğu**: <PERSON>gor<PERSON>, varyantlar, modifierlar
- **Ödeme İşlemleri**: <PERSON><PERSON><PERSON>, kredi <PERSON>, yeme<PERSON> kart<PERSON>
- **<PERSON><PERSON>**: Masa bi<PERSON>ştirme/ayırma, rezervasyon
- **Mutfak Entegrasyonu**: Kitchen Display System (KDS)

### 📊 İş Yönetimi
- **Stok Takibi**: Otomatik stok hareketleri, kritik seviye uyarıları
- **Reçete Yönetimi**: Malzeme tüketimi, maliyet hesaplama
- **Raporlama**: Günlük Z-raporu, satış analizleri
- **Çoklu Şube**: Merkezi yönetim, şubeler arası senkronizasyon

### 🔗 Entegrasyonlar
- **E-Dönüşüm**: E-Arşiv, E-Fatura desteği
- **Online Platformlar**: Yemeksepeti, Getir, Trendyol entegrasyonu
- **Mali Cihazlar**: ÖKC, EFT-POS terminal entegrasyonu
- **SMS/Email**: Müşteri bildirimleri

### 👥 CRM ve Sadakat
- **Müşteri Yönetimi**: Detaylı müşteri profilleri
- **Sadakat Programı**: Puan sistemi, kampanyalar
- **Rezervasyon**: Masa rezervasyon sistemi
- **Pazarlama**: Segmentasyon, hedefli kampanyalar

## 🛠️ Teknoloji Stack

### Backend
- **Framework**: NestJS v11 (TypeScript)
- **Veritabanı**: PostgreSQL + Prisma ORM v6.12
- **Authentication**: JWT + Passport.js
- **API Docs**: Swagger/OpenAPI
- **Validation**: class-validator + class-transformer

### Frontend
- **Desktop**: Electron v37
- **UI Framework**: React v19 + TypeScript
- **UI Library**: Microsoft Fluent UI v9
- **Build Tool**: Vite v7
- **Styling**: CSS Modules + Fluent UI tokens

## 🚀 Kurulum

### Gereksinimler
- Node.js 18+ 
- PostgreSQL 14+
- npm veya yarn

### 1. Repository'yi klonlayın
```bash
git clone https://github.com/yourusername/atropos.git
cd atropos
```

### 2. Backend Kurulumu
```bash
cd atropos-backend
npm install

# Veritabanı bağlantısını yapılandırın
cp .env.example .env
# .env dosyasını düzenleyin

# Prisma migration'ları çalıştırın
npx prisma migrate dev
npx prisma generate

# Backend'i başlatın
npm run start:dev
```

### 3. Frontend Kurulumu
```bash
cd ../atropos-frontend-desktop
npm install

# Electron uygulamasını başlatın
npm run dev
```

## 📖 Kullanım

### Backend API
Backend API varsayılan olarak `http://localhost:3000` adresinde çalışır.

**Swagger UI**: `http://localhost:3000/api`

### Desktop Uygulaması
Electron uygulaması otomatik olarak açılır ve backend API'sine bağlanır.

**Varsayılan Giriş Bilgileri**:
- Username: `admin`
- Password: `admin123`

### Temel İş Akışı
1. **Giriş Yapın**: Admin kullanıcısı ile giriş yapın
2. **Şirket Oluşturun**: İlk şirket kaydını yapın
3. **Şube Ekleyin**: Şube bilgilerini girin
4. **Ürün Kataloğu**: Kategoriler ve ürünler ekleyin
5. **Masa Düzeni**: Masa alanları ve masaları oluşturun
6. **Sipariş Alın**: İlk siparişinizi oluşturun

## 📚 API Dokümantasyonu

API dokümantasyonu Swagger UI üzerinden erişilebilir:
`http://localhost:3000/api`

### Ana Endpoint'ler
- `POST /auth/login` - Kullanıcı girişi
- `GET /auth/profile` - Kullanıcı profili
- `POST /order` - Sipariş oluşturma
- `GET /product` - Ürün listesi
- `POST /payment` - Ödeme işlemi

## 🗄️ Veritabanı Şeması

Proje 40+ tablo içeren kapsamlı bir veritabanı şemasına sahiptir:

### Ana Modeller
- **Company**: Şirket bilgileri
- **Branch**: Şube yönetimi
- **User**: Kullanıcı ve roller
- **Product**: Ürün kataloğu
- **Order**: Sipariş yönetimi
- **Payment**: Ödeme işlemleri
- **Customer**: Müşteri yönetimi
- **InventoryItem**: Stok takibi

Detaylı şema: `atropos-backend/prisma/schema.prisma`

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

### Geliştirme Kuralları
- TypeScript kullanın
- ESLint kurallarına uyun
- Test yazın
- Commit mesajlarını anlamlı yazın

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 📞 İletişim

- **Proje Sahibi**: [Your Name]
- **Email**: <EMAIL>
- **LinkedIn**: [Your LinkedIn]

## 🙏 Teşekkürler

- [NestJS](https://nestjs.com/) - Backend framework
- [Prisma](https://prisma.io/) - Database ORM
- [Fluent UI](https://developer.microsoft.com/en-us/fluentui) - UI components
- [Electron](https://electronjs.org/) - Desktop app framework

---

⭐ Bu projeyi beğendiyseniz yıldız vermeyi unutmayın!
