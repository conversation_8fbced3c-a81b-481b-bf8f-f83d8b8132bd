## Objectives and Key Results (OKRs)

Quarterly, we plan using OKRs

- Use [OKRs](Fluent-UI-Objectives) to set our goals and measurable results
- Update our [Roadmap](Fluent-UI-React-Roadmap) with our goals for the quarter

## Project cycle

Monthly, we execute on a project cycle.

- Leading up to and during the first week of the cycle we identify work to be done in the next cycle GitHub Issues (Projects or Bugs) are added to the [Milestone](https://github.com/microsoft/fluentui/milestones) corresponding to the cycle (example: [November Project Cycle](https://github.com/microsoft/fluentui/milestone/19)).
- Update project issues with project proposals for the work (example: [vNext bundle size requirements and tooling support #17678](https://github.com/microsoft/fluentui/issues/17678)).
- A project cycle by weeks
  - **Week 1:**
    - Proposals are reviewed and work starts
  - **Weeks 2,3+:** Work proceeds according to proposal
  - **Last week:**
    - Work wraps up, close out issues
    - Work for next cycle identified
    - Proposals for next cycle are written
    - In the rare case work should continue to the next milestone, move it
- Each week the whole team syncs on high level progress

## Starting a project cycle:

At the beginning of the month we:

- Review proposals from the last month
  - Close out the ones that are complete
  - Review whether or not we hit our goals we set for the month
- Determine the projects we'll work in upcoming month
  - If the project will span months, set the goal for this month
- Add any bugs that we plan to fix to the milestone

## Weekly project updates

Every week we:

- Go through each project in the current [Milestone](https://github.com/microsoft/fluentui/milestones)
- Discuss and enter updates in a comment for each open Project
  - Bonus points for entering your updates in the issue comment ahead of time!
- Briefly cover bugs tracked in the Milestone

## What is a project proposal?

Properties of well shaped projects:
From: [Principles of Shaping](https://basecamp.com/shapeup/1.1-chapter-02#property-1-its-rough)

1. Rough
   - Unfinished, not too much detail
   - Room for real contributions from others
2. Solved
   - Despite unfinished, it is thought through
   - All the pieces at the macro level are there and fit together
   - Clear direction showing the approach
   - Obvious holes and risks have been removed
   - Call out how we expect to resolve anticipated problems
3. Bounded
   - Also indicates what _not_ to do
   - There is a specific amount of time to spend on the project
   - Scope has been limited to fit the appetite for investment

> Taken together, the **roughness** leaves room for the team to resolve all the details, while the **solution** and **boundaries** act like guard rails. They reduce risk and channel the team’s efforts, making sure they don’t build too much, wander around, or get stuck.

### Steps to shaping

From: [Steps to shaping](https://basecamp.com/shapeup/1.1-chapter-02#steps-to-shaping)

1. [Set boundaries](https://basecamp.com/shapeup/1.2-chapter-03)
   - Figure out how much time the idea is worth (Appetite)
   - Narrow down the the problem
2. Rough out the solution (or "elements")
   Iterate quickly, likely alone or with a partner to produce: - An idea that solves the problem within the appetite, but without all the fine details worked out
3. Address [risks and "rabbit holes"](https://basecamp.com/shapeup/1.4-chapter-05)
   This step is critical and what allows us to execute predictably - Find holes and issues that could trip up the team implementing the solution - Add details or reduce scope to prevent the team from getting stuck or wasting time
4. [Write the pitch](https://basecamp.com/shapeup/1.4-chapter-05)
   - Once we shaped the project enough to bet on it write it up
   - The pitch summarizes the problem, constraints, solution, rabbit holes, and limitations
   - Used to kick off the project and explain it to the team that will build it

### Project template

- Full [Project template](Fluent-UI-Project-Template) - with extra info
- [Create a new Project Proposal](https://github.com/microsoft/fluentui/issues/new?body=%23%20Project%20Proposal%0A%0A%23%23%20Problem%0A%23%23%20Appetite%0A%23%23%20Solution%0A%23%23%20Risks%20%28Rabbit%20holes%29%0A%23%23%20Out%20of%20scope%20%28No-gos%29&labels=Type:%20Feature) - Don't forget to pick a Milestone!
