_This document explains RFC review process for `@fluentui/react-components`_

The goal of this process is to ensure that RFCs and specs have the required attention and that authors receive meaningful feedback.

Currently we have the following teams that work on the library (each team has a CREW LEAD):

- @microsoft/cxe-coastal
- @microsoft/cxe-prg
- @microsoft/cxe-red
- @microsoft/teams-prg

RFCs and specs that don't have REVIEWERS are announced at the beginning of offline Tech Syncs that happen weekly on Wednesdays. The goal of a CREW LEAD is to assign a REVIEWER from their team and provide an estimation for an initial review. A TEAM may also skip a review, but that decision should be recorded on a PR through a comment. A CREW LEAD should comment on a PR by Friday EOD.

Examples of a comment:

> @gh-handle will review on behalf @microsoft/TEAM by 30 Apr.

> Our team will not review this PR, we kindly request that you use your best judgement in determining the next steps for this pull request 🐱

## Notes

- A comment from a CREW LEAD provides clarity on when and who will participate in reviews
- RFCs/specs can still be reviewed by anyone
- Estimation date is only for providing an initial review, this does not mean that RFC/spec will be merged that day
