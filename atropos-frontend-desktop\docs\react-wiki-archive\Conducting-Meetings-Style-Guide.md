# Conducting Meetings Style Guide

> Authors: <AUTHORS>

<!-- to update toc, copy this file to local machine and run `npx markdown-toc -i file-name.md` -->

<!-- toc -->

- [Style Guide](#style-guide)
  - [Agenda](#agenda)
  - [Recording](#recording)
  - [Communication during meeting](#communication-during-meeting)
  - [Meeting roles](#meeting-roles)
    - [Meeting lead/Moderator](#meeting-leadmoderator)
    - [<PERSON><PERSON><PERSON>](#scribe)

<!-- tocstop -->

This document describes best practices on how to conduct meetings within Fluent UI web organization.

**Why:**

Based on conversation between Tech Leads and managers we identified following set of problems:

- A lot of context might be shared during a meeting that is not being followed up or gets lost in huge threads of chat.
- Although we record our meetings, chat is not being “recorded”, video cannot be searched, which adds excessive time allocation overhead to person that didn’t attend but wants/needs to stay in loop or follow up on topic.

Based on these identified issues here is a simple guidance how to conduct meeting.

## Style Guide

### Agenda

Agenda should be provided in advance via Loop Meeting Notes functionality.

> 💡 TIP: write <kbd>//</kbd> + <kbd>ENTER</kbd> to generate your name

- Every Agenda topic ends with name of author

_Example of Agenda item:_

`//@alias / <Topic>` --- translates to --> `💬 @Martin / Write a style guide for drinking beer`

or

`<Topic> / [<Author>]` --- translates to --> `Write a style guide for drinking beer / [Martin]`

### Recording

Meeting should be recorded, so it can be viewed by anyone that wasn't able to attend or in generale to have information being transparently shared.

### Communication during meeting

1. Use Teams meeting “raise hand” feature if one wants to contribute to a topic
2. Use meeting notes

   > (💡 TIP: write <kbd>//</kbd> + <kbd>ENTER</kbd> to generate your name)

   - to ask questions
   - to provide comments/answers

3. Use the Teams chat as last resort of communication on particular topic. Feel free to use it for memes, pictures, making jokes/fun comments.

### Meeting roles

Every meeting should have 2 roles:

- Meeting Lead/Moderator
- Scribe

#### Meeting Lead/Moderator

- Drives the meeting agenda
- Verbally summarizes the outcome of discussed topic (then Scribe writes it down)
- Checks chat/raise hand to give folks space to talk

#### Scribe

- Provides additional context/summary/action points based on conversation on particular topic to the meeting notes
- If needed he copies conversations/opinions from meeting chat

_💡 Example of Scribe notes in meeting notes:_

![Example of Scribe notes in meeting notes](https://user-images.githubusercontent.com/1223799/154324271-9f88bbe3-91fd-4839-9873-f542553996c5.png)
