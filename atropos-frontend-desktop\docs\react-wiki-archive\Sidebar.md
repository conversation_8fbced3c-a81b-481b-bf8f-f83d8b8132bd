### What's new

- [FAQ - Fabric and Stardust to Fluent UI](FAQ---Fabric-and-Stardust-to-Fluent-UI)
- `@fluentui/react` Version 9
  - [Release Schedule](Fluent-UI-React-v9-Release)
  - [Component Roadmap](Fluent-UI-React-v9-Component-Roadmap)
- `@fluentui/react` Version 8
  - [Release notes](Version-8-release-notes)
  - [Migration guide](Version-8-migration-guide)
  - [Internet Explorer 11 Sunset](Internet-Explorer-11-Sunset)
- [Contributing to the `7.0` branch](Contributing-to-the-7.0-branch)
- [How to apply themes (version 7/8)](How-to-apply-theme-to-Fluent-UI-React-components)

### Planning

- [Fluent UI React roadmap](Fluent-UI-React-Roadmap)
- [Monthly project cycles](Fluent-UI-Project-Cycles)

### Process

- [Planning and development process](Fluent-UI-Dev-Process) (for work by the core team)
  - [Project template](Fluent-UI-Project-Template)
  - [Project retrospective template](Fluent-UI-Project-Retrospective-Template)
- [Conducting meetings Style guide](Conducting-Meetings-Style-Guide)
- [Keeping up with review requests](Keeping-up-with-review-requests)
- [RFC review process](RFC-review-process)

### Usage

- [Setup (configuring your environment)](Setup)
- Fluent UI React version 7/8
  - [Getting started](Getting-Started-with-Fluent-UI-React)
  - [Applying themes](How-to-apply-theme-to-Fluent-UI-React-components)
  - [Using icons](Using-icons)
  - [Customizing fonts](Customizing-fonts)
  - [Server-side rendering and browserless testing](Server-side-rendering-and-browserless-testing)

### Reporting issues

- [Known External Issues](Known-External-Issues)
- [Overview](Reporting-Issues)
- [Accessibility Troubleshooting](Accessibility-Troubleshooting)

### Contributing

- [CLA](CLA)
- [Overview](Contributing)
- [Repo structure](Repo-structure)
- Development process
  - [Setup](Setup) (and development basics)
  - [Development workflow](Development-Workflow)
  - [Using local (unpublished) version of the lib with a local React app](<https://github.com/microsoft/fluentui/wiki/Using-local-(unpublished)-version-of-the-lib-with-a-local-React-app>)
  - [Build commands (master, 7.0)](Build-Commands)
  - [Change files](Change-Files)
- Contributing to previous versions
  - [Contributing to the `7.0` branch](Contributing-to-the-7.0-branch)
  - [Legacy branches (5.0, 6.0)](Legacy-Branches)
    - [Cherry-picking to 6.0](Cherry-Picking-fixes-from-Fabric-7.0-to-6.0)
  - [Build commands (5.0, 6.0 branches)](<Build-Commands-(5.0,-6.0-Branches)>)
- [API Extractor](API-Extractor)
- [Build command changes](Build-Command-Changes) made in early 2020

### Component creation and convergence

- [Component implementation guide](Component-Implementation-Guide)
- [Creating a component](New-Components)
- [Implementation Best Practices](Implementation-Best-Practices)
- [Theming](Theming)
- [Documenting](API-Documentation)
- [Styling (old approach)](Component-Styling)

### Testing

- [Overview](Testing)
- [Testing with Jest](Testing-with-Jest)
- [E2E testing (Cypress)](E2E-testing-with-Cypress)
- [Visual testing (Screener)](Visual-regression-testing-with-Screener)
- [Accessibility review checklist](Manual-Accessibility-Review-Checklist)

### Coding guidelines

- [Coding style](Coding-Style)
- [React guidelines](React-Guidelines)
- [TypeScript guidelines](TypeScript-Guidelines)

### Best practices

- [Performance](Performance)
- [Accessibility](Accessibility)
- [Advanced usage](Advanced-Usage)
- [Component design](Component-Design)
- [Component usage](Component-Usage)
- [Deprecation guidelines](Deprecation-Guidelines)

### References

- [FAQ](FAQ)
- [Browser support](Browser-Support)
- [mergeStyles reference](https://github.com/microsoft/fluentui/blob/master/packages/merge-styles/README.md)
- [Perf testing](Perf-Testing)
- [Layer & portals](Layer-&-Portals)
- [Migration from 4.x to 5.x](Fabric5)
- [Fabric 7.0 release notes](Fabric-7)

### Useful tools

- [Advanced auto-merge commands](Advanced-auto-merge)
