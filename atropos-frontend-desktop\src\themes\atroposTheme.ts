// atropos-frontend-desktop/src/themes/atroposTheme.ts
import { createLightTheme } from '@fluentui/react-components';

// Microsoft'un Windows 11 renk paletinden esinlenerek temel renkleri tanımlayalım
// Daha fazla renk token'ı için Fluent UI dokümantasyonuna bakılabilir.
// https://react.fluentui.dev/?path=/docs/concepts-tokens-overview--docs
const customColors = {
  // Accent colors (Brand) - Windows 11 mavisinden ilhamla
  colorBrandBackground: '#0078D4', // Mavi
  colorBrandBackgroundHover: '#005FB8',
  colorBrandBackgroundPressed: '#004381',
  colorBrandBackgroundSelected: '#005FB8',
  colorBrandForeground1: '#0078D4',
  colorBrandForegroundLink: '#0078D4',
  colorBrandForegroundLinkHover: '#005FB8',
  colorBrandForegroundLinkPressed: '#004381',
  colorBrandStroke1: '#0078D4',

  // Neutral colors (Arka planlar, metin<PERSON>, kena<PERSON><PERSON><PERSON><PERSON>) - <PERSON><PERSON><PERSON>k tema için
  colorNeutralBackground1: '#FFFFFF', // Beyaz (ana arka plan)
  colorNeutralBackground2: '#F0F0F0', // Çok açık gri (panel arka planları)
  colorNeutralBackground3: '#E8E8E8', // Daha koyu açık gri
  colorNeutralBackground4: '#D8D8D8',
  colorNeutralBackground5: '#C8C8C8',
  colorNeutralBackgroundAlpha: 'rgba(255, 255, 255, 0.7)', // Mica/Acrylic için şeffaf beyaz simülasyonu

  colorNeutralForeground1: '#1F1F1F', // Koyu gri (ana metin)
  colorNeutralForeground2: '#4A4A4A',
  colorNeutralForeground3: '#6B6B6B',
  colorNeutralForeground4: '#878787', // Daha açık gri metin
  colorNeutralForegroundDisabled: '#C8C8C8',

  colorNeutralStroke1: '#E0E0E0', // Kenarlıklar
  colorNeutralStrokeAccessible: '#A19F9D',
  colorNeutralStrokeAlpha: 'rgba(0, 0, 0, 0.1)', // Hafif şeffaf kenarlık
  colorNeutralStrokeDisabled: '#E0E0E0',

  // Gölgeler (Windows 11'in daha yumuşak gölgeleri için ayarlar)
  shadow2: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  shadow4: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  shadow8: '0 6px 10px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  shadow16: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  shadow28: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
  shadow64: '0 20px 40px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22)',
};

// Mevcut varsayılan açık temayı alalım
const defaultLightTheme = createLightTheme();

// Özel renklerimizi varsayılan tema üzerine merge edelim
export const atroposTheme = {
  ...defaultLightTheme,
  // Fluent UI'ın kendi renk paletlerini customColors ile birleştiriyoruz.
  // Burada, Fluent UI'ın semantic token'larına karşılık gelen renkleri override ediyoruz.
  // Örneğin, colorBrandBackground, tüm birincil aksan renklerini etkiler.

  // Marka Renkleri
  colorBrandBackground: customColors.colorBrandBackground,
  colorBrandBackgroundHover: customColors.colorBrandBackgroundHover,
  colorBrandBackgroundPressed: customColors.colorBrandBackgroundPressed,
  colorBrandBackgroundSelected: customColors.colorBrandBackgroundSelected,
  colorBrandForeground1: customColors.colorBrandForeground1,
  colorBrandForegroundLink: customColors.colorBrandForegroundLink,
  colorBrandForegroundLinkHover: customColors.colorBrandForegroundLinkHover,
  colorBrandForegroundLinkPressed: customColors.colorBrandForegroundLinkPressed,
  colorBrandStroke1: customColors.colorBrandStroke1,

  // Nötr Renkler
  colorNeutralBackground1: customColors.colorNeutralBackground1,
  colorNeutralBackground2: customColors.colorNeutralBackground2,
  colorNeutralBackground3: customColors.colorNeutralBackground3,
  colorNeutralBackground4: customColors.colorNeutralBackground4,
  colorNeutralBackground5: customColors.colorNeutralBackground5,
  colorNeutralBackgroundAlpha: customColors.colorNeutralBackgroundAlpha,

  colorNeutralForeground1: customColors.colorNeutralForeground1,
  colorNeutralForeground2: customColors.colorNeutralForeground2,
  colorNeutralForeground3: customColors.colorNeutralForeground3,
  colorNeutralForeground4: customColors.colorNeutralForeground4,
  colorNeutralForegroundDisabled: customColors.colorNeutralForegroundDisabled,

  colorNeutralStroke1: customColors.colorNeutralStroke1,
  colorNeutralStrokeAccessible: customColors.colorNeutralStrokeAccessible,
  colorNeutralStrokeAlpha: customColors.colorNeutralStrokeAlpha,
  colorNeutralStrokeDisabled: customColors.colorNeutralStrokeDisabled,

  // Gölgeler
  shadow2: customColors.shadow2,
  shadow4: customColors.shadow4,
  shadow8: customColors.shadow8,
  shadow16: customColors.shadow16,
  shadow28: customColors.shadow28,
  shadow64: customColors.shadow64,

  // Yuvarlak köşeler ve diğer geometrik ayarlar (Windows 11'e benzetmek için)
  borderRadiusNone: '0px',
  borderRadiusSmall: '2px',
  borderRadiusMedium: '4px',
  borderRadiusLarge: '8px', // Windows 11'de daha belirgin yuvarlaklık
  borderRadiusXLarge: '12px', // Daha büyük bileşenler için daha fazla yuvarlaklık
  borderRadiusCircular: '9999px',
  
  // Diğer token'lar (isteğe bağlı olarak)
  // fontFamilyBase: "'Segoe UI', 'Helvetica Neue', Arial, sans-serif", // Font özelleştirmesi
};
