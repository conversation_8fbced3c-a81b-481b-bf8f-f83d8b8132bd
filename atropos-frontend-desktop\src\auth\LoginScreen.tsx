// src/auth/LoginScreen.tsx
import React, { useState } from 'react';
import {
  Button,
  Field,
  Input,
  makeStyles,
  shorthands,
  tokens,
  Text,
  Spinner,
  Card,
  CardHeader,
  Avatar,
} from '@fluentui/react-components';
import { CheckmarkCircleFilled, DismissCircleFilled } from '@fluentui/react-icons'; // İkonlar için

// Stil tanımlamaları (Windows 11 login ekranından esinlenerek)
const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    ...shorthands.padding('48px'), // <PERSON>ha geniş padding
    backgroundColor: tokens.colorNeutralBackgroundAlpha, // atroposTheme'deki yarı saydam renk
    ...shorthands.borderRadius(tokens.borderRadiusXLarge), // <PERSON>ha belirgin yuvar<PERSON> köşeler
    boxShadow: tokens.shadow64, // <PERSON>ha belirgin bir gölge
    backdropFilter: 'blur(30px) saturate(180%)', // Arka planı bulanıklaştırma (Electron desteklerse, deneysel)
    border: `1px solid ${tokens.colorNeutralStrokeAlpha}`, // Hafif saydam kenarlık
    textAlign: 'center', // İçeriği ortala
    minWidth: '450px', // Genişletilmiş boyut
    minHeight: '450px',
    position: 'relative', // Arka plan resmi için pozisyonlama
    zIndex: 1, // Diğer öğelerin önüne geçmesini sağla
  },
  loginBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    ...shorthands.gap('24px'), // Daha geniş boşluklar
    width: '100%',
    maxWidth: '350px', // İçerik genişliğini kısıtla
  },
  avatarContainer: {
    ...shorthands.gap('12px'), // Avatar ve isim arasındaki boşluk
  },
  form: {
    ...shorthands.gap('20px'), // Form elemanları arasındaki boşluk
  },
  message: {
    textAlign: 'center',
    ...shorthands.padding('8px'),
    ...shorthands.borderRadius('4px'),
  },
  success: {
    backgroundColor: tokens.colorPaletteGreenBackground3,
    color: tokens.colorPaletteGreenForeground3,
  },
  error: {
    backgroundColor: tokens.colorPaletteRedBackground3,
    color: tokens.colorPaletteRedForeground3,
  },
  // Windows 11 login ekranındaki 'Sign-in options' ve 'Other user' benzeri alanlar için
  options: {
    display: 'flex',
    ...shorthands.gap('12px'),
    marginTop: '10px',
  },
  otherUser: {
    marginTop: '20px',
    ...shorthands.padding('10px 0'),
    borderTop: `1px solid ${tokens.colorNeutralStroke1}`,
    width: '100%',
    textAlign: 'center',
  },
});

interface LoginScreenProps {
  onLoginSuccess: (token: string) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const styles = useStyles();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault(); // Formun varsayılan gönderimini engelle
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Giriş başarısız oldu.');
      }

      const data = await response.json();
      setSuccessMessage('Giriş başarılı! Yönlendiriliyorsunuz...');
      // Token'ı bir sonraki adımda güvenli bir şekilde saklayacağız
      onLoginSuccess(data.access_token);
    } catch (err: any) {
      setError(err.message);
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.loginBox}>
        <div className={styles.avatarContainer}>
          <Avatar name={username || "Kullanıcı"} size={96} /> {/* Varsayılan avatar */}
          <Text as="h2" size={600}>{username || "Kullanıcı Adı"}</Text>
          <Text size={200}>Lütfen şifrenizi girin.</Text>
        </div>

        <form onSubmit={handleLogin} className={styles.form}>
          <Field label="Kullanıcı Adı" required>
            <Input
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Kullanıcı adınız"
              disabled={loading}
            />
          </Field>
          <Field label="Şifre" required>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Şifreniz"
              disabled={loading}
            />
          </Field>

          {error && (
            <Card className={`${styles.message} ${styles.error}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Hata:</Text>}
                description={<Text size={200}>{error}</Text>}
                action={<DismissCircleFilled primaryFill={tokens.colorPaletteRedForeground3} />}
              />
            </Card>
          )}
          {successMessage && (
            <Card className={`${styles.message} ${styles.success}`} appearance="outline">
              <CardHeader
                header={<Text weight="semibold">Başarılı:</Text>}
                description={<Text size={200}>{successMessage}</Text>}
                action={<CheckmarkCircleFilled primaryFill={tokens.colorPaletteGreenForeground3} />}
              />
            </Card>
          )}

          <Button type="submit" appearance="primary" disabled={loading}>
            {loading ? <Spinner size="tiny" labelPosition="after" label="Giriş Yapılıyor..." /> : 'Giriş Yap'}
          </Button>
        </form>

        {/* Windows 11 Login ekranındaki diğer seçenekler benzeri butonlar */}
        <div className={styles.options}>
          <Button appearance="subtle">Giriş Seçenekleri</Button>
          <Button appearance="subtle">PIN ile Giriş</Button>
        </div>

        <Text className={styles.otherUser} size={200} weight="semibold">
          <Button appearance="subtle">Başka bir kullanıcı</Button>
        </Text>
      </div>
    </div>
  );
};

export default LoginScreen;
